import { redirect } from "next/navigation";

import { getCurrentUser } from "@/lib/session";
import { constructMetadata } from "@/lib/utils";
import { DashboardHeader } from "@/components/dashboard/header";
import UsersList from "@/components/admin/users-list";

export const metadata = constructMetadata({
  title: "Settings – Next Template",
  description: "Configure your account and website settings.",
});

export default async function SettingsPage() {
  const user = await getCurrentUser();

  if (!user?.id) redirect("/login");

  return (
    <>
      <div className="pb-10">
        <UsersList user={user} />
      </div>
    </>
  );
}
