import { auth } from "@/auth"
import { prisma } from "@/lib/db"

export const GET = auth(async (req, { params }: { params: { id: string } }) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 })
  }

  const currentUser = req.auth.user
  if (!currentUser || currentUser?.role !== "ADMIN") {
    return new Response("Not authorized", { status: 403 })
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: params.id },
    })

    if (!user) {
      return new Response("User not found", { status: 404 })
    }

    return Response.json(user)
  } catch (error) {
    console.error("Error fetching user:", error)
    return new Response("Internal server error", { status: 500 })
  }
})

export const PUT = auth(async (req, { params }: { params: { id: string } }) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 })
  }

  const currentUser = req.auth.user
  if (!currentUser || currentUser?.role !== "ADMIN") {
    return new Response("Not authorized", { status: 403 })
  }

  try {
    const body = await req.json()
    const { name, email, role, status, plan, country } = body

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
    })

    if (!existingUser) {
      return new Response("User not found", { status: 404 })
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: params.id },
      data: {
        ...(name && { name }),
        ...(email && { email }),
        ...(role && { role }),
        ...(status && { status }),
        ...(plan && { plan }),
        ...(country !== undefined && { country }),
      },
    })

    return Response.json(updatedUser)
  } catch (error) {
    console.error("Error updating user:", error)
    return new Response("Internal server error", { status: 500 })
  }
})

export const DELETE = auth(async (req, { params }: { params: { id: string } }) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 })
  }

  const currentUser = req.auth.user
  if (!currentUser || currentUser?.role !== "ADMIN") {
    return new Response("Not authorized", { status: 403 })
  }

  try {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
    })

    if (!existingUser) {
      return new Response("User not found", { status: 404 })
    }

    // Delete user
    await prisma.user.delete({
      where: { id: params.id },
    })

    return Response.json({ message: "User deleted successfully" })
  } catch (error) {
    console.error("Error deleting user:", error)
    return new Response("Internal server error", { status: 500 })
  }
})
