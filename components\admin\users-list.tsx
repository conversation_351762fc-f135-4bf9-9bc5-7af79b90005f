"use client"
import { useEffect, useState } from "react"
import type { User } from "@/types"
import { AddUserDialog } from "./user/add-user-dialog"
import { EditUserDialog } from "./user/edit-user-dialog"
import { DeleteUserDialog } from "./user/delete-user-dialog"
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Plus, Columns3, MoreHorizontal } from "lucide-react"
import { ArrowUpDown } from "lucide-react"
import { Search } from "lucide-react"
import { DashboardHeader } from "../dashboard/header"

const STATUS_OPTIONS = ["active", "inactive"]
const ROLE_OPTIONS = ["USER", "ADMIN"]

const COLUMNS = [
    { id: "name", label: "Name" },
    { id: "role", label: "Role" },
    { id: "email", label: "Email" },
    { id: "gender", label: "Gender" },
    { id: "dateOfBirth", label: "Date of Birth" },
    { id: "mobile", label: "Mobile" },
    { id: "status", label: "Status" },
]

export default function UsersList(user: { id: string; role: string }) {
    const [loading, setLoading] = useState(true)
    const [search, setSearch] = useState("")
    const [statusFilter, setStatusFilter] = useState<string[]>([])
    const [roleFilter, setRoleFilter] = useState<string[]>([])
    const [planFilter, setPlanFilter] = useState<string[]>([])
    const [sortBy, setSortBy] = useState("name")
    const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
    const [selectedUsers, setSelectedUsers] = useState<string[]>([])
    const [visibleColumns, setVisibleColumns] = useState<string[]>(COLUMNS.map((col) => col.id))
    const [addDialogOpen, setAddDialogOpen] = useState(false)
    const [editDialogOpen, setEditDialogOpen] = useState(false)
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const [selectedUser, setSelectedUser] = useState<User | null>(null)

    const [users, setUsers] = useState<any[]>([])
    useEffect(() => {
        fetchUsers()
    }, [search, statusFilter, roleFilter, planFilter, sortBy, sortOrder])

    const fetchUsers = async () => {
        setLoading(true)
        try {
            const params = new URLSearchParams()
            if (search) params.append("search", search)
            if (statusFilter.length > 0) params.append("status", statusFilter.join(","))
            if (roleFilter.length > 0) params.append("role", roleFilter.join(","))
            if (planFilter.length > 0) params.append("plan", planFilter.join(","))
            params.append("sortBy", sortBy)
            params.append("sortOrder", sortOrder)

            const response = await fetch(`/api/users?${params.toString()}`)
            if (response.ok) {
                const data = await response.json()
                setUsers(data.users)
                console.log(data.users)
            }
        } catch (error) {
            console.error("Error fetching users:", error)
        } finally {
            setLoading(false)
        }
    }

    const handleSort = (column: string) => {
        if (sortBy === column) {
            setSortOrder(sortOrder === "asc" ? "desc" : "asc")
        } else {
            setSortBy(column)
            setSortOrder("asc")
        }
    }

    const toggleSelectAll = () => {
        if (selectedUsers.length === users.length) {
            setSelectedUsers([])
        } else {
            setSelectedUsers(users.map((user) => user.id))
        }
    }

    const toggleSelectUser = (userId: string) => {
        setSelectedUsers((prev) => (prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]))
    }

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case "active":
                return "default"
            case "PENDING":
                return "secondary"
            case "inactive":
                return "destructive"
            default:
                return "outline"
        }
    }

    const formatRole = (role: string) => {
        return role
            .split("_")
            .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
            .join(" ")
    }

    const getInitials = (name: string) => {
        return name
            .split(" ")
            .map((n) => n[0])
            .join("")
            .toUpperCase()
            .slice(0, 2)
    }

    return (
        <div className="">
            <DashboardHeader
                heading="Users"
                text="Manage users and for this org."
            >
                <div className="mb-8 flex items-center justify-between">
                    <Button onClick={() => setAddDialogOpen(true)} className="gap-2">
                        <Plus className="h-4 w-4" />
                        Add New User
                    </Button>
                </div>
            </DashboardHeader>
            <div className="mx-auto mt-4">
                {/* Filters */}
                <div className="mb-6 flex flex-wrap items-center gap-3">
                    <div className="relative flex-1 min-w-[300px]">
                        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                            placeholder="Search users..."
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                            className="pl-9 bg-card border-border"
                        />
                    </div>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="gap-2 bg-transparent">
                                <Plus className="h-4 w-4" />
                                Status
                                {statusFilter.length > 0 && (
                                    <Badge variant="secondary" className="ml-1 rounded-full px-1.5 py-0.5 text-xs">
                                        {statusFilter.length}
                                    </Badge>
                                )}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start" className="w-48">
                            {STATUS_OPTIONS.map((status) => (
                                <DropdownMenuCheckboxItem
                                    key={status}
                                    checked={statusFilter.includes(status)}
                                    onCheckedChange={(checked) => {
                                        setStatusFilter((prev) => (checked ? [...prev, status] : prev.filter((s) => s !== status)))
                                    }}
                                >
                                    {status}
                                </DropdownMenuCheckboxItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="gap-2 bg-transparent">
                                <Plus className="h-4 w-4" />
                                Plan
                                {planFilter.length > 0 && (
                                    <Badge variant="secondary" className="ml-1 rounded-full px-1.5 py-0.5 text-xs">
                                        {planFilter.length}
                                    </Badge>
                                )}
                            </Button>
                        </DropdownMenuTrigger>
                        {/* <DropdownMenuContent align="start" className="w-48">
              {PLAN_OPTIONS.map((plan) => (
                <DropdownMenuCheckboxItem
                  key={plan}
                  checked={planFilter.includes(plan)}
                  onCheckedChange={(checked) => {
                    setPlanFilter((prev) => (checked ? [...prev, plan] : prev.filter((p) => p !== plan)))
                  }}
                >
                  {plan}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent> */}
                    </DropdownMenu>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="gap-2 bg-transparent">
                                <Plus className="h-4 w-4" />
                                Role
                                {roleFilter.length > 0 && (
                                    <Badge variant="secondary" className="ml-1 rounded-full px-1.5 py-0.5 text-xs">
                                        {roleFilter.length}
                                    </Badge>
                                )}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start" className="w-56">
                            {ROLE_OPTIONS.map((role) => (
                                <DropdownMenuCheckboxItem
                                    key={role}
                                    checked={roleFilter.includes(role)}
                                    onCheckedChange={(checked) => {
                                        setRoleFilter((prev) => (checked ? [...prev, role] : prev.filter((r) => r !== role)))
                                    }}
                                >
                                    {formatRole(role)}
                                </DropdownMenuCheckboxItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>

                    <div className="ml-auto">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" className="gap-2 bg-transparent">
                                    <Columns3 className="h-4 w-4" />
                                    Columns
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                                {COLUMNS.map((column) => (
                                    <DropdownMenuCheckboxItem
                                        key={column.id}
                                        checked={visibleColumns.includes(column.id)}
                                        onCheckedChange={(checked) => {
                                            setVisibleColumns((prev) =>
                                                checked ? [...prev, column.id] : prev.filter((col) => col !== column.id),
                                            )
                                        }}
                                    >
                                        {column.label}
                                    </DropdownMenuCheckboxItem>
                                ))}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>

                <div className="rounded-lg border border-border bg-card overflow-x-auto">
                    <table className="w-full">
                        <thead>
                            <tr className="border-b border-border bg-muted/50">
                                <th className="px-4 py-3 text-left w-10">
                                    <Checkbox
                                        checked={selectedUsers.length === users.length && users.length > 0}
                                        onCheckedChange={toggleSelectAll}
                                    />
                                </th>
                                {visibleColumns.includes("name") && (
                                    <th className="px-4 py-3 text-left">
                                        <button
                                            onClick={() => handleSort("name")}
                                            className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                                        >
                                            Name
                                            <ArrowUpDown className="h-3 w-3" />
                                        </button>
                                    </th>
                                )}
                                {visibleColumns.includes("role") && (
                                    <th className="px-4 py-3 text-left">
                                        <button
                                            onClick={() => handleSort("role")}
                                            className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                                        >
                                            Role
                                            <ArrowUpDown className="h-3 w-3" />
                                        </button>
                                    </th>
                                )}
                                {visibleColumns.includes("email") && (
                                    <th className="px-4 py-3 text-left">
                                        <button
                                            onClick={() => handleSort("email")}
                                            className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                                        >
                                            Email
                                            <ArrowUpDown className="h-3 w-3" />
                                        </button>
                                    </th>
                                )}
                                {visibleColumns.includes("mobile") && (
                                    <th className="px-4 py-3 text-left">
                                        <button
                                            onClick={() => handleSort("mobile")}
                                            className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                                        >
                                            Mobile
                                            <ArrowUpDown className="h-3 w-3" />
                                        </button>
                                    </th>
                                )}
                                {visibleColumns.includes("gender") && (
                                    <th className="px-4 py-3 text-left">
                                        <button
                                            onClick={() => handleSort("gender")}
                                            className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                                        >
                                            Gender
                                            <ArrowUpDown className="h-3 w-3" />
                                        </button>
                                    </th>
                                )}
                                {visibleColumns.includes("dateOfBirth") && (
                                    <th className="px-4 py-3 text-left">
                                        <button
                                            onClick={() => handleSort("dateOfBirth")}
                                            className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                                        >
                                            Date of Birth
                                            <ArrowUpDown className="h-3 w-3" />
                                        </button>
                                    </th>
                                )}
                                {visibleColumns.includes("country") && (
                                    <th className="px-4 py-3 text-left">
                                        <button
                                            onClick={() => handleSort("country")}
                                            className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                                        >
                                            Country
                                            <ArrowUpDown className="h-3 w-3" />
                                        </button>
                                    </th>
                                )}
                                {visibleColumns.includes("status") && (
                                    <th className="px-4 py-3 text-left">
                                        <button
                                            onClick={() => handleSort("status")}
                                            className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                                        >
                                            Status
                                            <ArrowUpDown className="h-3 w-3" />
                                        </button>
                                    </th>
                                )}
                                <th className="px-4 py-3 w-10"></th>
                            </tr>
                        </thead>
                        <tbody>
                            {loading ? (
                                <tr>
                                    <td colSpan={visibleColumns.length + 2} className="px-4 py-12 text-center">
                                        <div className="text-muted-foreground">Loading...</div>
                                    </td>
                                </tr>
                            ) : users.length === 0 ? (
                                <tr>
                                    <td colSpan={visibleColumns.length + 2} className="px-4 py-12 text-center">
                                        <div className="text-muted-foreground">No users found</div>
                                    </td>
                                </tr>
                            ) : (
                                users.map((user) => (
                                    <tr key={user.id} className="border-b border-border hover:bg-accent/50 transition-colors">
                                        <td className="px-4 py-2">
                                            <Checkbox
                                                checked={selectedUsers.includes(user.id)}
                                                onCheckedChange={() => toggleSelectUser(user.id)}
                                            />
                                        </td>
                                        {visibleColumns.includes("name") && (
                                            <td className="px-4 py-2">
                                                <div className="flex items-center gap-3">
                                                    <Avatar className="h-8 w-8">
                                                        <AvatarImage src={user.image || undefined} alt={user.name} />
                                                        <AvatarFallback className="bg-muted text-muted-foreground text-sm">
                                                            {getInitials(user.name)}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <span className="font-medium text-foreground whitespace-nowrap">{user.name}</span>
                                                </div>
                                            </td>
                                        )}
                                        {visibleColumns.includes("role") && (
                                            <td className="px-4 py-2">
                                                <span className="text-foreground whitespace-nowrap">{formatRole(user.role)}</span>
                                            </td>
                                        )}
                                        {visibleColumns.includes("email") && (
                                            <td className="px-4 py-2">
                                                <span className="text-muted-foreground">{user.email}</span>
                                            </td>
                                        )}
                                        {visibleColumns.includes("mobile") && (
                                            <td className="px-4 py-2">
                                                <span className="text-foreground whitespace-nowrap">{user.mobileNumber}</span>
                                            </td>
                                        )}
                                        {visibleColumns.includes("gender") && (
                                            <td className="px-4 py-2">
                                                <span className="text-foreground">{user.gender}</span>
                                            </td>
                                        )}
                                        {visibleColumns.includes("dateOfBirth") && (
                                            <td className="px-4 py-2">
                                                <span className="text-foreground whitespace-nowrap">
                                                    {user.dateOfBirth ? new Date(user.dateOfBirth).toLocaleDateString("en-GB") : "N/A"}
                                                </span>
                                            </td>
                                        )}
                                        {visibleColumns.includes("status") && (
                                            <td className="px-4 py-2">
                                                <Badge
                                                    variant={getStatusBadgeVariant(user.status)}
                                                    className={
                                                        user.status === "active"
                                                            ? "bg-green-100 text-green-700 border-green-300 rounded-md capitalize hover:bg-green-200"
                                                            : user.status === "pending"
                                                                ? "bg-yellow-100 text-yellow-700 border-yellow-300 rounded-md capitalize hover:bg-yellow-200"
                                                                : "bg-red-100 text-red-700 border-red-300 rounded-md capitalize hover:bg-red-200"
                                                    }
                                                >
                                                    {user.status}
                                                </Badge>

                                            </td>
                                        )}
                                        <td className="px-4 py-2">
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" size="icon" className="h-8 w-8">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuItem
                                                        onClick={() => {
                                                            setSelectedUser(user)
                                                            setEditDialogOpen(true)
                                                        }}
                                                    >
                                                        Edit
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                    <DropdownMenuItem
                                                        onClick={() => {
                                                            setSelectedUser(user)
                                                            setDeleteDialogOpen(true)
                                                        }}
                                                        className="text-destructive"
                                                    >
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </div>

            <AddUserDialog open={addDialogOpen} onOpenChange={setAddDialogOpen} onSuccess={fetchUsers} />
            <EditUserDialog
                open={editDialogOpen}
                onOpenChange={setEditDialogOpen}
                user={selectedUser}
                onSuccess={fetchUsers}
            />
            <DeleteUserDialog
                open={deleteDialogOpen}
                onOpenChange={setDeleteDialogOpen}
                user={selectedUser}
                onSuccess={fetchUsers}
            />
        </div>
    )
}