
import { auth } from "@/auth";

import { prisma } from "@/lib/db";

export const GET = auth(async (req) => {
    if (!req.auth) {
        return new Response("Not authenticated", { status: 401 })
    }

    const currentUser = req.auth.user
    if (!currentUser) {
        return new Response("Invalid user", { status: 401 })
    }

    if (currentUser?.role !== "ADMIN") {
        return new Response("Not authorized", { status: 403 })
    }

    try {
        const { searchParams } = new URL(req.url)

        // Extract query parameters
        const search = searchParams.get("search") || ""
        const status = searchParams.get("status") || ""
        const role = searchParams.get("role") || ""
        const plan = searchParams.get("plan") || ""
        const sortBy = searchParams.get("sortBy") || "name"
        const sortOrder = searchParams.get("sortOrder") || "asc"
        const page = Number.parseInt(searchParams.get("page") || "1")
        const limit = Number.parseInt(searchParams.get("limit") || "50")

        // Build where clause
        const where: any = {
            emailVerified: { not: null },
        }

        // Add search filter
        if (search) {
            where.OR = [
                { name: { contains: search, mode: "insensitive" } },
                { email: { contains: search, mode: "insensitive" } },
                { country: { contains: search, mode: "insensitive" } },
            ]
        }

        // Add status filter
        if (status && status !== "") {
            where.status = status
        }

        // Add role filter
        if (role) {
            where.role = {
                in: role.split(","), // Example: ?role=USER,ADMIN
            }
        }

        // Add plan filter
        if (plan) {
            where.plan = plan
        }

        // Build orderBy clause
        const orderBy: any = {}
        orderBy[sortBy] = sortOrder

        // Get total count for pagination
        const total = await prisma.user.count({ where })

        // Fetch users with filters and pagination
        const users = await prisma.user.findMany({
            where,
            orderBy,
            skip: (page - 1) * limit,
            take: limit,
        })

        return Response.json({
            users,
            pagination: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        })
    } catch (error) {
        console.error("Error fetching users:", error)
        return new Response("Internal server error", {
            status: 500,
            statusText: JSON.stringify(error),
        })
    }
})

export const POST = auth(async (req) => {
    if (!req.auth) {
        return new Response("Not authenticated", { status: 401 })
    }

    const currentUser = req.auth.user
    if (!currentUser || currentUser?.role !== "ADMIN") {
        return new Response("Not authorized", { status: 403 })
    }

    try {
        const body = await req.json()
        const { name, email, role, status, plan, country } = body

        // Validate required fields
        if (!name || !email) {
            return new Response("Name and email are required", { status: 400 })
        }

        // Check if user already exists
        const existingUser = await prisma.user.findUnique({
            where: { email },
        })

        if (existingUser) {
            return new Response("User with this email already exists", {
                status: 409,
            })
        }

        // Create new user
        const newUser = await prisma.user.create({
            data: {
                name,
                email,
                role: role || "USER",
                status: status || "PENDING",
                plan: plan || "BASIC",
                country: country || "",
                emailVerified: new Date(),
            },
        })

        return Response.json(newUser, { status: 201 })
    } catch (error) {
        console.error("Error creating user:", error)
        return new Response("Internal server error", { status: 500 })
    }
})